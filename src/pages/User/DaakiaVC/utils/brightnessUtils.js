// Brightness manager utility - stateless functions like virtualBackground.js
import { RoomEvent } from "livekit-client";


let participantBrightness = new Map();
let rpcRegistered = false;
let throttleTimer = null;
let lastThrottleTime = 0;
let currentRoom = null;

let handleParticipantDisconnected = null;
let handleRoomDisconnected = null;
let handleConnectionStateChanged = null;

const handleRpcMessage = (data) => {
  try {
    const payload = JSON.parse(data.payload);
    const { brightness } = payload;
    participantBrightness.set(data.callerIdentity, brightness);
    return "Brightness updated successfully";
  } catch (error) {
    console.error('Brightness RPC processing failed:', error.message);
    return "Error: Failed to update brightness";
  }
};

// Initialize brightness RPC for a room (like toggleBlur function)
export async function initializeBrightnessRPC(room) {
  if (!room || room.state !== 'connected') {
    return;
  }

  // Prevent double registration
  if (currentRoom === room && rpcRegistered) {
    return;
  }

  try {
    // IMPORTANT: Always unregister first to prevent "overriding RPC handler" error
    // This is required by LiveKit - must call unregisterRpcMethod before registerRpcMethod
    try {
      room.localParticipant.unregisterRpcMethod('setBrightness');
    } catch (error) {
      // This is expected if no method was registered before
    }

    // Now register the RPC method
    room.localParticipant.registerRpcMethod('setBrightness', handleRpcMessage);

    rpcRegistered = true;
    currentRoom = room;

    // Setup participant event listeners - store references for cleanup
    handleParticipantDisconnected = (participant) => {
      participantBrightness.delete(participant.identity);
    };

    handleRoomDisconnected = () => {
      participantBrightness.clear();
      rpcRegistered = false;
      currentRoom = null;
    };

    handleConnectionStateChanged = (state) => {
      // Reset RPC registration if connection is lost
      if (state !== 'connected') {
        rpcRegistered = false;
      }
    };

    room.on(RoomEvent.ParticipantDisconnected, handleParticipantDisconnected);
    room.on(RoomEvent.Disconnected, handleRoomDisconnected);
    room.on(RoomEvent.ConnectionStateChanged, handleConnectionStateChanged);

  } catch (error) {
    console.error('Failed to register brightness RPC method:', error.message);
    rpcRegistered = false;
  }
}

// Separate function for the actual send operation
async function sendBrightnessImmediate(room, brightness) {
  try {
    const remoteParticipants = Array.from(room.remoteParticipants.values());

    if (remoteParticipants.length === 0) {
      return;
    }

    // Send to all participants
    const sendPromises = remoteParticipants.map(async (participant) => {
      try {
        const response = await room.localParticipant.performRpc({
          destinationIdentity: participant.identity,
          method: 'setBrightness',
          payload: JSON.stringify({ brightness }),
          responseTimeout: 5000,
        });
        return { participant: participant.identity, success: true, response };
      } catch (error) {
        console.error(`Failed to send brightness to ${participant.identity}:`, error.message);
        return { participant: participant.identity, success: false, error: error.message };
      }
    });

    await Promise.all(sendPromises);
  } catch (error) {
    console.error('Brightness send operation failed:', error.message);
  }
}

// Send brightness to all participants (like toggleBlur function)
export async function sendBrightnessToAll(room, brightness) {
  if (!room || room.state !== 'connected') {
    return;
  }

  if (brightness === 100) {
    return;
  }

  // Throttle the send operation for better UI responsiveness
  const now = Date.now();
  const throttleDelay = 150; // 150ms throttle for smooth UI updates

  if (now - lastThrottleTime < throttleDelay) {
    // Clear existing timer and set new one
    if (throttleTimer) {
      clearTimeout(throttleTimer);
    }

    throttleTimer = setTimeout(() => {
      lastThrottleTime = Date.now();
      sendBrightnessImmediate(room, brightness);
    }, throttleDelay - (now - lastThrottleTime));
    return;
  }

  lastThrottleTime = now;
  sendBrightnessImmediate(room, brightness);
}

export async function sendBrightnessToNewParticipant(room, participant, brightness) {
  if (!room || room.state !== 'connected') {
    return;
  }

  if (!participant || !participant.identity) {
    return;
  }

  if (brightness === 100) {
    return; // No need to send default brightness
  }

  // Wait a bit for participant to be ready
  setTimeout(async () => {
    try {
      await room.localParticipant.performRpc({
        destinationIdentity: participant.identity,
        method: 'setBrightness',
        payload: JSON.stringify({ brightness }),
        responseTimeout: 5000,
      });
    } catch (error) {
      console.error(`Failed to send brightness to new participant ${participant.identity}:`, error.message);
    }
  }, 3000);
}

// Get all participant brightness values (simple getter)
export function getAllParticipantBrightness() {
  return new Map(participantBrightness);
}

// Cleanup brightness RPC (like noEffect function)
export function cleanupBrightnessRPC() {
  if (currentRoom && rpcRegistered) {
    try {
      currentRoom.localParticipant.unregisterRpcMethod('setBrightness');
    } catch (error) {
      console.error('Failed to unregister brightness RPC method:', error.message);
    }
    try {
      if (handleParticipantDisconnected) {
        currentRoom.off(RoomEvent.ParticipantDisconnected, handleParticipantDisconnected);
      }
      if (handleRoomDisconnected) {
        currentRoom.off(RoomEvent.Disconnected, handleRoomDisconnected);
      }
      if (handleConnectionStateChanged) {
        currentRoom.off(RoomEvent.ConnectionStateChanged, handleConnectionStateChanged);
      }
    } catch (error) {
      console.error('Failed to remove brightness event listeners:', error.message);
    }
  }

  if (throttleTimer) {
    clearTimeout(throttleTimer);
    throttleTimer = null;
  }
  lastThrottleTime = 0;

  // Reset all state and handler references
  rpcRegistered = false;
  currentRoom = null;
  participantBrightness.clear();
  handleParticipantDisconnected = null;
  handleRoomDisconnected = null;
  handleConnectionStateChanged = null;
}
